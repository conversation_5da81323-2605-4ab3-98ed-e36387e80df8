<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microfinance Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>Microfinance System</h3>
            </div>

            <ul class="list-unstyled components">
                <li class="active">
                    <a href="#homeSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                    <ul class="collapse list-unstyled" id="homeSubmenu">
                        <li><a href="#">Overview</a></li>
                        <li><a href="#">Quick Actions</a></li>
                    </ul>
                </li>
                
                <li>
                    <a href="#loanSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-hand-holding-usd"></i> Loan Portfolio
                    </a>
                    <ul class="collapse list-unstyled" id="loanSubmenu">
                        <li><a href="#">Active Loans</a></li>
                        <li><a href="#">Loan Applications</a></li>
                        <li><a href="#">Risk Assessment</a></li>
                    </ul>
                </li>
                
                <li>
                    <a href="#savingsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-piggy-bank"></i> Savings & Collection
                    </a>
                    <ul class="collapse list-unstyled" id="savingsSubmenu">
                        <li><a href="#">Savings Accounts</a></li>
                        <li><a href="#">Collection Tracking</a></li>
                        <li><a href="#">Payment History</a></li>
                    </ul>
                </li>
                
                <li>
                    <a href="#disbursementSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-money-bill-wave"></i> Disbursement
                    </a>
                    <ul class="collapse list-unstyled" id="disbursementSubmenu">
                        <li><a href="#">Fund Allocation</a></li>
                        <li><a href="#">Disbursement Tracker</a></li>
                    </ul>
                </li>
                
                <li>
                    <a href="#complianceSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-shield-alt"></i> Compliance
                    </a>
                    <ul class="collapse list-unstyled" id="complianceSubmenu">
                        <li><a href="#">Audit Trail</a></li>
                        <li><a href="#">Compliance Reports</a></li>
                    </ul>
                </li>
                
                <li>
                    <a href="#reportSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                    <ul class="collapse list-unstyled" id="reportSubmenu">
                        <li><a href="#">Financial Reports</a></li>
                        <li><a href="#">Performance Metrics</a></li>
                        <li><a href="#">Custom Reports</a></li>
                    </ul>
                </li>
                
                <li>
                    <a href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-users-cog"></i> User Management
                    </a>
                    <ul class="collapse list-unstyled" id="userSubmenu">
                        <li><a href="#">Users</a></li>
                        <li><a href="#">Roles & Permissions</a></li>
                        <li><a href="#">Activity Logs</a></li>
                    </ul>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="ms-auto">
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle fa-lg"></i> Admin User
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="#">Profile</a></li>
                                    <li><a class="dropdown-item" href="#">Settings</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#">Logout</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="container-fluid">
                <h2>Dashboard Overview</h2>
                <div class="row mt-4">
                    <div class="col-md-3 mb-4">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <h5 class="card-title">Total Loans</h5>
                                <h2 class="card-text">$1,245,678</h2>
                                <p class="card-text"><small>+12% from last month</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <h5 class="card-title">Total Savings</h5>
                                <h2 class="card-text">$567,890</h2>
                                <p class="card-text"><small>+8% from last month</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <h5 class="card-title">Active Borrowers</h5>
                                <h2 class="card-text">1,245</h2>
                                <p class="card-text"><small>+45 this month</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card text-white bg-danger">
                            <div class="card-body">
                                <h5 class="card-title">Portfolio at Risk</h5>
                                <h2 class="card-text">2.8%</h2>
                                <p class="card-text"><small>-0.5% from last month</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>Loan Portfolio Overview</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="loanChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>Collection Efficiency</h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="gauge-container">
                                    <canvas id="gaugeChart" width="200" height="200"></canvas>
                                    <div class="gauge-label">98.5%</div>
                                </div>
                                <p class="mt-3">On-time collection rate</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>Recent Transactions</h5>
                                <button class="btn btn-sm btn-outline-primary">View All</button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Date</th>
                                                <th>Client</th>
                                                <th>Type</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>#1001</td>
                                                <td>2025-07-13</td>
                                                <td>John Doe</td>
                                                <td><span class="badge bg-primary">Loan Disbursement</span></td>
                                                <td>$5,000.00</td>
                                                <td><span class="badge bg-success">Completed</span></td>
                                                <td><button class="btn btn-sm btn-outline-info">View</button></td>
                                            </tr>
                                            <tr>
                                                <td>#1002</td>
                                                <td>2025-07-12</td>
                                                <td>Jane Smith</td>
                                                <td><span class="badge bg-success">Repayment</span></td>
                                                <td>$250.00</td>
                                                <td><span class="badge bg-success">Completed</span></td>
                                                <td><button class="btn btn-sm btn-outline-info">View</button></td>
                                            </tr>
                                            <tr>
                                                <td>#1003</td>
                                                <td>2025-07-12</td>
                                                <td>Robert Johnson</td>
                                                <td><span class="badge bg-info">Savings Deposit</span></td>
                                                <td>$1,000.00</td>
                                                <td><span class="badge bg-success">Completed</span></td>
                                                <td><button class="btn btn-sm btn-outline-info">View</button></td>
                                            </tr>
                                            <tr>
                                                <td>#1004</td>
                                                <td>2025-07-11</td>
                                                <td>Emily Davis</td>
                                                <td><span class="badge bg-warning">Loan Application</span></td>
                                                <td>$3,500.00</td>
                                                <td><span class="badge bg-warning">Pending</span></td>
                                                <td><button class="btn btn-sm btn-outline-info">Review</button></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Calculator Modal -->
    <div class="modal fade" id="loanCalculatorModal" tabindex="-1" aria-labelledby="loanCalculatorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="loanCalculatorModalLabel">Loan Calculator</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="loanCalculatorForm">
                        <div class="mb-3">
                            <label for="loanAmount" class="form-label">Loan Amount ($)</label>
                            <input type="number" class="form-control" id="loanAmount" required>
                        </div>
                        <div class="mb-3">
                            <label for="interestRate" class="form-label">Interest Rate (%)</label>
                            <input type="number" step="0.01" class="form-control" id="interestRate" required>
                        </div>
                        <div class="mb-3">
                            <label for="loanTerm" class="form-label">Loan Term (months)</label>
                            <input type="number" class="form-control" id="loanTerm" required>
                        </div>
                        <div class="alert alert-info" id="calculationResult">
                            <strong>Monthly Payment:</strong> <span id="monthlyPayment">-</span><br>
                            <strong>Total Interest:</strong> <span id="totalInterest">-</span>
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn btn-primary" id="calculateBtn">Calculate</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Floating Button -->
    <div class="quick-actions">
        <button class="btn btn-primary btn-floating" id="quickActionsBtn" data-bs-toggle="tooltip" data-bs-placement="left" title="Quick Actions">
            <i class="fas fa-bolt"></i>
        </button>
        <div class="quick-actions-menu">
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#loanCalculatorModal">
                <i class="fas fa-calculator me-2"></i>Loan Calculator
            </button>
            <button class="btn btn-info" id="newClientBtn">
                <i class="fas fa-user-plus me-2"></i>New Client
            </button>
            <button class="btn btn-warning" id="newPaymentBtn">
                <i class="fas fa-hand-holding-usd me-2"></i>Record Payment
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="js/script.js"></script>
</body>
</html>
