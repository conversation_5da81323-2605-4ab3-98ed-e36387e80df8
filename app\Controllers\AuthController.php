<?php

class AuthController extends BaseController {
    public function login() {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'username' => 'required',
            'password' => 'required|min:6'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Find user by username or email
            $stmt = $this->db->prepare("SELECT * FROM users WHERE username = ? OR email = ? LIMIT 1");
            $stmt->execute([$data['username'], $data['username']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Verify user exists and password is correct
            if (!$user || !password_verify($data['password'], $user['password_hash'])) {
                return $this->error('Invalid username or password', 401);
            }
            
            // Check if account is active
            if ($user['status'] !== 'active') {
                return $this->error('Your account is ' . $user['status'], 403);
            }
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Generate JWT token
            $token = JWT::encode([
                'user_id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role']
            ]);
            
            // Return user data and token
            return $this->success([
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'full_name' => $user['full_name'],
                    'role' => $user['role']
                ],
                'token' => $token
            ]);
            
        } catch (Exception $e) {
            return $this->error('Login failed: ' . $e->getMessage());
        }
    }
    
    public function register() {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'username' => 'required|min:3|max:50|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
            'full_name' => 'required|min:3|max:100'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Hash password
            $passwordHash = password_hash($data['password'], PASSWORD_BCRYPT);
            
            // Start transaction
            $this->db->beginTransaction();
            
            // Create user
            $stmt = $this->db->prepare("
                INSERT INTO users (username, email, password_hash, full_name, role, status)
                VALUES (?, ?, ?, ?, 'client', 'active')
            ");
            
            $stmt->execute([
                $data['username'],
                $data['email'],
                $passwordHash,
                $data['full_name']
            ]);
            
            $userId = $this->db->lastInsertId();
            
            // Generate JWT token
            $token = JWT::encode([
                'user_id' => $userId,
                'username' => $data['username'],
                'email' => $data['email'],
                'role' => 'client'
            ]);
            
            // Commit transaction
            $this->db->commit();
            
            // Return success response
            return $this->success([
                'user' => [
                    'id' => $userId,
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'full_name' => $data['full_name'],
                    'role' => 'client'
                ],
                'token' => $token
            ], 'Registration successful', 201);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            return $this->error('Registration failed: ' . $e->getMessage());
        }
    }
    
    public function me() {
        $token = JWT::getTokenFromHeader();
        
        if (!$token) {
            return $this->error('Not authenticated', 401);
        }
        
        try {
            $decoded = JWT::decode($token);
            
            // Get fresh user data
            $stmt = $this->db->prepare("SELECT id, username, email, full_name, role, status FROM users WHERE id = ?");
            $stmt->execute([$decoded['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return $this->error('User not found', 404);
            }
            
            return $this->success(['user' => $user]);
            
        } catch (Exception $e) {
            return $this->error('Authentication failed: ' . $e->getMessage(), 401);
        }
    }
    
    private function updateLastLogin($userId) {
        $stmt = $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
    }
}
