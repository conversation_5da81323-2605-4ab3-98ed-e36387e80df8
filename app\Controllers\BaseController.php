<?php

class BaseController {
    protected $db;
    protected $request;
    protected $response;
    protected $args;
    
    public function __construct($request, $response, $args) {
        $this->db = Database::getInstance()->getConnection();
        $this->request = $request;
        $this->response = $response;
        $this->args = $args;
    }
    
    protected function json($data, $status = 200) {
        return $this->response->withJson($data, $status);
    }
    
    protected function success($data = null, $message = 'Success', $status = 200) {
        return $this->json([
            'status' => 'success',
            'message' => $message,
            'data' => $data
        ], $status);
    }
    
    protected function error($message = 'An error occurred', $status = 400, $errors = null) {
        $response = [
            'status' => 'error',
            'message' => $message
        ];
        
        if ($errors) {
            $response['errors'] = $errors;
        }
        
        return $this->json($response, $status);
    }
    
    protected function validate($rules) {
        $data = $this->request->getParsedBody();
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $rulesArray = explode('|', $rule);
            $value = $data[$field] ?? null;
            
            foreach ($rulesArray as $singleRule) {
                $params = [];
                
                if (strpos($singleRule, ':') !== false) {
                    list($singleRule, $param) = explode(':', $singleRule, 2);
                    $params = explode(',', $param);
                }
                
                $method = 'validate' . ucfirst($singleRule);
                
                if (method_exists($this, $method)) {
                    $result = $this->$method($field, $value, $params);
                    if ($result !== true) {
                        $errors[$field][] = $result;
                        break;
                    }
                }
            }
        }
        
        if (!empty($errors)) {
            return $this->error('Validation failed', 422, $errors);
        }
        
        return true;
    }
    
    // Validation methods
    protected function validateRequired($field, $value, $params) {
        if (empty($value)) {
            return "The {$field} field is required";
        }
        return true;
    }
    
    protected function validateEmail($field, $value, $params) {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return "The {$field} must be a valid email address";
        }
        return true;
    }
    
    protected function validateMin($field, $value, $params) {
        $min = (int) $params[0];
        if (strlen($value) < $min) {
            return "The {$field} must be at least {$min} characters";
        }
        return true;
    }
    
    protected function validateMax($field, $value, $params) {
        $max = (int) $params[0];
        if (strlen($value) > $max) {
            return "The {$field} may not be greater than {$max} characters";
        }
        return true;
    }
    
    protected function validateNumeric($field, $value, $params) {
        if (!is_numeric($value)) {
            return "The {$field} must be a number";
        }
        return true;
    }
    
    protected function validateIn($field, $value, $params) {
        if (!in_array($value, $params)) {
            $options = implode(', ', $params);
            return "The {$field} must be one of: {$options}";
        }
        return true;
    }
    
    protected function validateUnique($field, $value, $params) {
        $table = $params[0];
        $column = $params[1] ?? $field;
        $ignoreId = $params[2] ?? null;
        
        $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?";
        $params = [$value];
        
        if ($ignoreId) {
            $query .= " AND id != ?";
            $params[] = $ignoreId;
        }
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $count = $stmt->fetch()['count'];
        
        if ($count > 0) {
            return "The {$field} has already been taken";
        }
        
        return true;
    }
}
