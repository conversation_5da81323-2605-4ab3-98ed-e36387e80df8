<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once '../config/database.php';
require_once 'auth.php';

// Get request method and URI
$request_method = $_SERVER['REQUEST_METHOD'];
$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri_parts = explode('/', trim($request_uri, '/'));

// Route the request
$endpoint = isset($uri_parts[1]) ? $uri_parts[1] : '';
$id = isset($uri_parts[2]) ? $uri_parts[2] : null;

// JWT Authentication
$auth = new Auth();
$user_data = null;

// Public endpoints that don't require authentication
$public_endpoints = ['login', 'register', 'forgot-password'];

if (!in_array($endpoint, $public_endpoints)) {
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $token = str_replace('Bearer ', '', $headers['Authorization']);
    }
    
    if (!$token) {
        http_response_code(401);
        echo json_encode(["message" => "Access denied. Token is required."]);
        exit();
    }
    
    try {
        $user_data = $auth->validateToken($token);
    } catch (Exception $e) {
        http_response_code(401);
        echo json_encode(["message" => $e->getMessage()]);
        exit();
    }
}

// Include the appropriate controller
$controller_file = 'controllers/' . ucfirst($endpoint) . 'Controller.php';

if (file_exists($controller_file)) {
    require_once $controller_file;
    $controller_name = ucfirst($endpoint) . 'Controller';
    $controller = new $controller_name();
    
    // Handle the request
    switch ($request_method) {
        case 'GET':
            if ($id) {
                $controller->get($id);
            } else {
                $controller->getAll();
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            $controller->create($data);
            break;
        case 'PUT':
            $data = json_decode(file_get_contents('php://input'), true);
            $controller->update($id, $data);
            break;
        case 'DELETE':
            $controller->delete($id);
            break;
        default:
            http_response_code(405);
            echo json_encode(["message" => "Method not allowed"]);
            break;
    }
} else {
    http_response_code(404);
    echo json_encode(["message" => "Endpoint not found"]);
}
?>
